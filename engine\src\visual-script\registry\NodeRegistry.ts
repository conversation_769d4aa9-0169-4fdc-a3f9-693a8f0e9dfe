/**
 * 视觉脚本节点注册系统
 * 统一管理所有节点的注册、分类和编辑器集成
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  // 核心系统
  CORE = 'core',
  ENTITY_MANAGEMENT = 'entity_management',
  COMPONENT_MANAGEMENT = 'component_management',
  TRANSFORM = 'transform',
  
  // 渲染系统
  RENDERING = 'rendering',
  MATERIAL = 'material',
  LIGHTING = 'lighting',
  CAMERA = 'camera',
  POST_PROCESSING = 'post_processing',
  
  // 物理系统
  PHYSICS = 'physics',
  SOFT_BODY = 'soft_body',
  
  // 动画系统
  ANIMATION = 'animation',
  ADVANCED_ANIMATION = 'advanced_animation',
  
  // 音频系统
  AUDIO = 'audio',
  ADVANCED_AUDIO = 'advanced_audio',
  
  // 输入系统
  INPUT = 'input',
  ADVANCED_INPUT = 'advanced_input',
  SENSOR_INPUT = 'sensor_input',
  VR_AR_INPUT = 'vr_ar_input',
  
  // 网络系统
  NETWORK = 'network',
  
  // AI系统
  AI = 'ai',
  AI_DEEP_LEARNING = 'ai_deep_learning',
  AI_MACHINE_LEARNING = 'ai_machine_learning',
  AI_TOOLS = 'ai_tools',
  AI_SERVICES = 'ai_services',
  AI_COMPUTER_VISION = 'ai_computer_vision',
  COMPUTER_VISION = 'computer_vision',
  NLP = 'nlp',
  MACHINE_LEARNING = 'machine_learning',
  
  // 场景系统
  SCENE = 'scene',
  SCENE_EDITING = 'scene_editing',
  SCENE_MANAGEMENT = 'scene_management',
  TERRAIN = 'terrain',
  WATER = 'water',
  PARTICLES = 'particles',

  // 资源系统
  RESOURCES = 'resources',
  RESOURCE_MANAGEMENT = 'resource_management',
  RESOURCE_OPTIMIZATION = 'resource_optimization',
  
  // 工业系统
  INDUSTRIAL = 'industrial',
  MES = 'mes',
  QUALITY = 'quality',
  MAINTENANCE = 'maintenance',
  INDUSTRIAL_MES = 'industrial_mes',
  INDUSTRIAL_DEVICE = 'industrial_device',
  INDUSTRIAL_MAINTENANCE = 'industrial_maintenance',
  INDUSTRIAL_QUALITY = 'industrial_quality',
  INDUSTRIAL_SUPPLY_CHAIN = 'industrial_supply_chain',
  INDUSTRIAL_ENERGY = 'industrial_energy',
  
  // 边缘计算
  EDGE_COMPUTING = 'edge_computing',
  
  // 专业应用
  BLOCKCHAIN = 'blockchain',
  LEARNING_RECORD = 'learning_record',
  RAG_APPLICATION = 'rag_application',
  SPATIAL_INFO = 'spatial_info',
  DIGITAL_HUMAN = 'digital_human',
  
  // 项目管理
  PROJECT = 'project',
  COLLABORATION = 'collaboration',
  
  // 数据服务
  DATA = 'data',
  FILE = 'file',
  AUTH = 'auth',
  
  // UI系统
  UI = 'ui',
  
  // 动作捕捉
  MOTION_CAPTURE = 'motion_capture'
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  name: string;
  description: string;
  category: NodeCategory;
  nodeClass: new (nodeType: string, name: string, id?: string) => VisualScriptNode;
  icon?: string;
  color?: string;
  tags?: string[];
  deprecated?: boolean;
  experimental?: boolean;
}

/**
 * 节点注册表管理器
 */
export class NodeRegistryManager {
  private nodes: Map<string, NodeInfo> = new Map();
  private categories: Map<NodeCategory, NodeInfo[]> = new Map();
  private initialized: boolean = false;

  /**
   * 获取单例实例
   */
  private static instance: NodeRegistryManager;
  
  static getInstance(): NodeRegistryManager {
    if (!NodeRegistryManager.instance) {
      NodeRegistryManager.instance = new NodeRegistryManager();
    }
    return NodeRegistryManager.instance;
  }

  /**
   * 初始化节点注册表
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    this.registerAllNodes();
    this.buildCategoryIndex();
    this.initialized = true;

    Debug.log('NodeRegistry', `节点注册完成: ${this.nodes.size}个节点, ${this.categories.size}个分类`);
  }

  /**
   * 注册节点
   */
  registerNode(nodeInfo: NodeInfo): void {
    if (this.nodes.has(nodeInfo.type)) {
      Debug.warn('NodeRegistry', `节点类型 ${nodeInfo.type} 已存在，将被覆盖`);
    }
    
    this.nodes.set(nodeInfo.type, nodeInfo);
    
    // 更新分类索引
    if (!this.categories.has(nodeInfo.category)) {
      this.categories.set(nodeInfo.category, []);
    }
    this.categories.get(nodeInfo.category)!.push(nodeInfo);
  }

  /**
   * 获取节点信息
   */
  getNode(type: string): NodeInfo | undefined {
    return this.nodes.get(type);
  }

  /**
   * 获取所有节点
   */
  getAllNodes(): NodeInfo[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 根据分类获取节点
   */
  getNodesByCategory(category: NodeCategory): NodeInfo[] {
    return this.categories.get(category) || [];
  }

  /**
   * 搜索节点
   */
  searchNodes(query: string): NodeInfo[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllNodes().filter(node => 
      node.name.toLowerCase().includes(lowerQuery) ||
      node.description.toLowerCase().includes(lowerQuery) ||
      node.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 创建节点实例
   */
  createNode(type: string, name?: string, id?: string): VisualScriptNode | null {
    const nodeInfo = this.getNode(type);
    if (!nodeInfo) {
      Debug.error('NodeRegistry', `未找到节点类型: ${type}`);
      return null;
    }

    try {
      return new nodeInfo.nodeClass(type, name || nodeInfo.name, id);
    } catch (error) {
      Debug.error('NodeRegistry', `创建节点失败: ${type}`, error);
      return null;
    }
  }

  /**
   * 清空注册表
   * 用于开发时的热重载
   */
  clear(): void {
    this.nodes.clear();
    this.categories.clear();
    this.initialized = false;
    Debug.log('NodeRegistry', '节点注册表已清空');
  }

  /**
   * 获取注册表状态
   */
  getStatus(): {
    initialized: boolean;
    nodeCount: number;
    categoryCount: number;
  } {
    return {
      initialized: this.initialized,
      nodeCount: this.nodes.size,
      categoryCount: this.categories.size
    };
  }

  /**
   * 注册所有节点
   */
  private registerAllNodes(): void {
    // 这里将分批注册所有节点
    // 由于节点数量庞大，我们将使用动态导入来避免循环依赖
    this.registerCoreNodes();
    this.registerRenderingNodes();
    this.registerPhysicsNodes();
    this.registerAnimationNodes();
    this.registerAudioNodes();
    this.registerInputNodes();
    this.registerNetworkNodes();
    this.registerAINodes();
    this.registerSceneNodes();
    this.registerResourceNodes();
    this.registerIndustrialNodes();
    this.registerEdgeComputingNodes();
    this.registerSpecializedNodes();
    this.registerDataServiceNodes();
    this.registerUINodes();
    this.registerMotionCaptureNodes();

    // 注册批次2.3-3.1节点
    this.registerBatch23To31NodesInternal();

    // 注册批次3.2-3.7节点
    this.registerBatch32To37NodesInternal();
  }

  /**
   * 注册核心节点
   */
  private registerCoreNodes(): void {
    // 由于避免循环依赖，这里使用动态导入
    // 实际的节点注册将通过外部注册函数完成
    Debug.log('NodeRegistry', '核心节点注册完成');
  }

  /**
   * 注册渲染节点
   */
  private registerRenderingNodes(): void {
    // 渲染节点将在后续添加
    Debug.log('NodeRegistry', '渲染节点注册完成');
  }

  /**
   * 注册物理节点
   */
  private registerPhysicsNodes(): void {
    // 物理节点将在后续添加
    Debug.log('NodeRegistry', '物理节点注册完成');
  }

  /**
   * 注册动画节点
   */
  private registerAnimationNodes(): void {
    // 动画节点将在后续添加
    Debug.log('NodeRegistry', '动画节点注册完成');
  }

  /**
   * 注册音频节点
   */
  private registerAudioNodes(): void {
    // 音频节点将在后续添加
    Debug.log('NodeRegistry', '音频节点注册完成');
  }

  /**
   * 注册输入节点
   */
  private registerInputNodes(): void {
    // 输入节点将在后续添加
    Debug.log('NodeRegistry', '输入节点注册完成');
  }

  /**
   * 注册网络节点
   */
  private registerNetworkNodes(): void {
    // 网络节点将在后续添加
    Debug.log('NodeRegistry', '网络节点注册完成');
  }

  /**
   * 注册AI节点
   */
  private registerAINodes(): void {
    try {
      // 导入并注册AI扩展节点
      import('./AIExtensionNodesRegistry').then(({ aiExtensionNodesRegistry }) => {
        aiExtensionNodesRegistry.registerAllNodes();
        Debug.log('NodeRegistry', 'AI扩展节点注册完成');
      }).catch(error => {
        Debug.error('NodeRegistry', 'AI扩展节点注册失败:', error);
      });

      Debug.log('NodeRegistry', 'AI节点注册完成');
    } catch (error) {
      Debug.error('NodeRegistry', 'AI节点注册失败:', error);
    }
  }

  /**
   * 注册场景节点
   */
  private registerSceneNodes(): void {
    // 场景节点将在后续添加
    Debug.log('NodeRegistry', '场景节点注册完成');
  }

  /**
   * 注册资源节点
   */
  private registerResourceNodes(): void {
    // 资源节点将在后续添加
    Debug.log('NodeRegistry', '资源节点注册完成');
  }

  /**
   * 注册工业节点
   */
  private registerIndustrialNodes(): void {
    // 工业节点将在后续添加
    Debug.log('NodeRegistry', '工业节点注册完成');
  }

  /**
   * 注册边缘计算节点
   */
  private registerEdgeComputingNodes(): void {
    // 边缘计算节点将在后续添加
    Debug.log('NodeRegistry', '边缘计算节点注册完成');
  }

  /**
   * 注册专业应用节点
   */
  private registerSpecializedNodes(): void {
    // 专业应用节点将在后续添加
    Debug.log('NodeRegistry', '专业应用节点注册完成');
  }

  /**
   * 注册数据服务节点
   */
  private registerDataServiceNodes(): void {
    // 数据服务节点将在后续添加
    Debug.log('NodeRegistry', '数据服务节点注册完成');
  }

  /**
   * 注册UI节点
   */
  private registerUINodes(): void {
    // UI节点将在后续添加
    Debug.log('NodeRegistry', 'UI节点注册完成');
  }

  /**
   * 注册动作捕捉节点
   */
  private registerMotionCaptureNodes(): void {
    // 动作捕捉节点将在后续添加
    Debug.log('NodeRegistry', '动作捕捉节点注册完成');
  }

  /**
   * 注册批次2.3-3.1节点（内部方法）
   */
  private registerBatch23To31NodesInternal(): void {
    try {
      // 导入并注册批次2.3-3.1节点
      import('./Batch23To31NodesRegistry').then(({ default: batch23To31NodesRegistry }) => {
        batch23To31NodesRegistry.registerAllNodes();
        Debug.log('NodeRegistry', '批次2.3-3.1节点注册完成 (41个节点)');
      }).catch(error => {
        Debug.error('NodeRegistry', '批次2.3-3.1节点注册失败:', error);
      });
    } catch (error) {
      Debug.error('NodeRegistry', '批次2.3-3.1节点注册失败:', error);
    }
  }

  /**
   * 注册批次3.2-3.7节点（内部方法）
   */
  private registerBatch32To37NodesInternal(): void {
    try {
      // 导入并注册批次3.2-3.7节点
      import('./Batch32To37NodesRegistry').then(({ batch32To37NodesRegistry }) => {
        batch32To37NodesRegistry.registerAllNodes();
        Debug.log('NodeRegistry', '批次3.2-3.7节点注册完成 (43个节点)');
      }).catch(error => {
        Debug.error('NodeRegistry', '批次3.2-3.7节点注册失败:', error);
      });
    } catch (error) {
      Debug.error('NodeRegistry', '批次3.2-3.7节点注册失败:', error);
    }
  }

  /**
   * 构建分类索引
   */
  private buildCategoryIndex(): void {
    this.categories.clear();
    
    for (const nodeInfo of this.nodes.values()) {
      if (!this.categories.has(nodeInfo.category)) {
        this.categories.set(nodeInfo.category, []);
      }
      this.categories.get(nodeInfo.category)!.push(nodeInfo);
    }
  }
}

/**
 * 导出节点注册表单例
 */
export const NodeRegistry = NodeRegistryManager.getInstance();

/**
 * 初始化节点注册表
 */
export function initializeNodeRegistry(): void {
  NodeRegistry.initialize();
}

/**
 * 批量注册节点的辅助函数
 */
export function registerNodes(nodes: NodeInfo[]): void {
  for (const nodeInfo of nodes) {
    NodeRegistry.registerNode(nodeInfo);
  }
}

/**
 * 创建节点信息的辅助函数
 */
export function createNodeInfo(
  type: string,
  name: string,
  description: string,
  category: NodeCategory,
  nodeClass: new (nodeType: string, name: string, id?: string) => VisualScriptNode,
  options?: {
    icon?: string;
    color?: string;
    tags?: string[];
    deprecated?: boolean;
    experimental?: boolean;
  }
): NodeInfo {
  return {
    type,
    name,
    description,
    category,
    nodeClass,
    icon: options?.icon,
    color: options?.color,
    tags: options?.tags,
    deprecated: options?.deprecated,
    experimental: options?.experimental
  };
}

/**
 * 获取节点统计信息
 */
export function getNodeStatistics(): {
  totalNodes: number;
  categoryCounts: Map<NodeCategory, number>;
  deprecatedCount: number;
  experimentalCount: number;
} {
  const allNodes = NodeRegistry.getAllNodes();
  const categoryCounts = new Map<NodeCategory, number>();
  let deprecatedCount = 0;
  let experimentalCount = 0;

  for (const node of allNodes) {
    // 统计分类
    const currentCount = categoryCounts.get(node.category) || 0;
    categoryCounts.set(node.category, currentCount + 1);

    // 统计特殊状态
    if (node.deprecated) deprecatedCount++;
    if (node.experimental) experimentalCount++;
  }

  return {
    totalNodes: allNodes.length,
    categoryCounts,
    deprecatedCount,
    experimentalCount
  };
}

/**
 * 验证节点注册表的完整性
 */
export function validateNodeRegistry(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const allNodes = NodeRegistry.getAllNodes();

  // 检查重复的节点类型
  const typeSet = new Set<string>();
  for (const node of allNodes) {
    if (typeSet.has(node.type)) {
      errors.push(`重复的节点类型: ${node.type}`);
    }
    typeSet.add(node.type);
  }

  // 检查节点类是否可实例化
  for (const node of allNodes) {
    try {
      const instance = NodeRegistry.createNode(node.type);
      if (!instance) {
        errors.push(`无法创建节点实例: ${node.type}`);
      }
    } catch (error) {
      errors.push(`节点类实例化失败: ${node.type} - ${error}`);
    }
  }

  // 检查废弃节点
  for (const node of allNodes) {
    if (node.deprecated) {
      warnings.push(`废弃的节点: ${node.type}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 注册批次2.3-3.1节点（交互体验与空间信息节点）
 */
export function registerBatch23To31Nodes(): void {
  try {
    // 导入并注册批次2.3-3.1节点
    import('./Batch23To31NodesRegistry').then(({ default: batch23To31NodesRegistry }) => {
      batch23To31NodesRegistry.registerAllNodes();
      Debug.log('NodeRegistry', '批次2.3-3.1节点注册完成 (41个节点)');
    }).catch(error => {
      Debug.error('NodeRegistry', '批次2.3-3.1节点注册失败:', error);
    });
  } catch (error) {
    Debug.error('NodeRegistry', '批次2.3-3.1节点注册失败:', error);
  }
}

/**
 * 注册批次3.2-3.7节点（数字人制作、区块链、学习记录、RAG、协作、第三方集成）
 */
export function registerBatch32To37Nodes(): void {
  try {
    // 导入并注册批次3.2-3.7节点
    import('./Batch32To37NodesRegistry').then(({ batch32To37NodesRegistry }) => {
      batch32To37NodesRegistry.registerAllNodes();
      Debug.log('NodeRegistry', '批次3.2-3.7节点注册完成 (43个节点)');
    }).catch(error => {
      Debug.error('NodeRegistry', '批次3.2-3.7节点注册失败:', error);
    });
  } catch (error) {
    Debug.error('NodeRegistry', '批次3.2-3.7节点注册失败:', error);
  }
}
